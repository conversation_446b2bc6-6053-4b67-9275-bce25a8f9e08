using CarPointCMS.Data;
using CarPointCMS.Models.Entities;
using CarPointCMS.Models.ViewModels;
using CarPointCMS.Controllers;
using CarPointCMS.Services;
using CarPointCMS.Services.Interfaces;
using CarPointCMS.Common;
using CarPointCMS.Attributes;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;

namespace CarPointCMS.Areas.Customer.Controllers
{
    [Area("Customer")]
    [CustomerOnly]
    public class CustomerController : BaseController
    {
        private readonly ApplicationDbContext _db;
        private readonly ILogger<CustomerController> _logger;
        private readonly IPaymentService _paymentService;
        private readonly IPackageService _packageService;
        private readonly ICurrencyService _currencyService;

        public CustomerController(
            ApplicationDbContext db,
            ILogger<CustomerController> logger,
            IAuthenticationService authService,
            IPaymentService paymentService,
            IPackageService packageService,
            ICurrencyService currencyService)
            : base(authService)
        {
            _db = db;
            _logger = logger;
            _paymentService = paymentService;
            _packageService = packageService;
            _currencyService = currencyService;
        }

        // GET: /Customer/Dashboard
        public async Task<IActionResult> Dashboard()
        {
            try
            {
                var customer = await GetCurrentCustomerAsync();
                if (customer == null)
                {
                    return RedirectToCustomerLogin();
                }

                var totalActiveListings = await _db.Listings
                    .CountAsync(l => l.UserId == customer.Id && l.ListingStatus == ListingStatus.Active);

                var totalPendingListings = await _db.Listings
                    .CountAsync(l => l.UserId == customer.Id && l.ListingStatus == ListingStatus.Pending);

                var currentPackage = await _db.PackagePurchases
                    .Include(pp => pp.Package)
                    .FirstOrDefaultAsync(pp => pp.UserId == customer.Id && pp.CurrentlyActive == true);

                var model = new CustomerDashboardViewModel
                {
                    Customer = customer,
                    TotalActiveListings = totalActiveListings,
                    TotalPendingListings = totalPendingListings,
                    CurrentPackage = currentPackage,
                    PageOther = await _db.PageOtherItems.FirstOrDefaultAsync() ?? new PageOtherItem()
                };

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading customer dashboard");
                return RedirectToAction("Login", "Account", new { Area = "Customer" });
            }
        }

        // GET: /Customer/Packages
        public async Task<IActionResult> Packages()
        {
            try
            {
                var customer = await GetCurrentCustomerAsync();
                if (customer == null)
                {
                    return RedirectToCustomerLogin();
                }

                var packages = await _db.Packages.ToListAsync();
                var currentPackage = await _db.PackagePurchases
                    .Include(pp => pp.Package)
                    .FirstOrDefaultAsync(pp => pp.UserId == customer.Id && pp.CurrentlyActive == true);

                var model = new CustomerPackagesViewModel
                {
                    Packages = packages,
                    CurrentPackage = currentPackage,
                    PageOther = await _db.PageOtherItems.FirstOrDefaultAsync() ?? new PageOtherItem()
                };

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading customer packages");
                return View(new CustomerPackagesViewModel());
            }
        }

        // GET: /Customer/PurchaseHistory
        public async Task<IActionResult> PurchaseHistory(int? pageIndex)
        {
            try
            {
                var customer = await GetCurrentCustomerAsync();
                if (customer == null)
                {
                    return RedirectToCustomerLogin();
                }

                var query = _db.PackagePurchases
                    .Include(pp => pp.Package)
                    .Where(pp => pp.UserId == customer.Id)
                    .OrderByDescending(pp => pp.CreatedAt);

                int pageSize = 10;
                var purchases = await PaginatedList<PackagePurchase>.CreateAsync(query, pageIndex ?? 1, pageSize);

                var model = new CustomerPurchaseHistoryViewModel
                {
                    Purchases = purchases,
                    PageOther = await _db.PageOtherItems.FirstOrDefaultAsync() ?? new PageOtherItem()
                };

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading customer purchase history");
                return View(new CustomerPurchaseHistoryViewModel());
            }
        }

        // GET: /Customer/MyListings
        public async Task<IActionResult> MyListings(int? pageIndex)
        {
            try
            {
                var customer = await GetCurrentCustomerAsync();
                if (customer == null)
                {
                    return RedirectToCustomerLogin();
                }

                var query = _db.Listings
                    .Include(l => l.ListingBrand)
                    .Include(l => l.ListingLocation)
                    .Where(l => l.UserId == customer.Id)
                    .OrderByDescending(l => l.CreatedAt);

                int pageSize = 10;
                var listings = await PaginatedList<Listing>.CreateAsync(query, pageIndex ?? 1, pageSize);

                var model = new CustomerListingsViewModel
                {
                    Listings = listings,
                    PageOther = await _db.PageOtherItems.FirstOrDefaultAsync() ?? new PageOtherItem()
                };

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading customer listings");
                return View(new CustomerListingsViewModel());
            }
        }

        // GET: /Customer/AddListing
        public async Task<IActionResult> AddListing()
        {
            try
            {
                var customer = await GetCurrentCustomerAsync();
                if (customer == null)
                {
                    return RedirectToCustomerLogin();
                }

                // Check if customer has an active package and listing allowance
                var currentPackage = await _db.PackagePurchases
                    .Include(pp => pp.Package)
                    .FirstOrDefaultAsync(pp => pp.UserId == customer.Id && pp.CurrentlyActive == true);

                if (currentPackage == null)
                {
                    SetErrorMessage("You need to purchase a package before adding listings.");
                    return RedirectToAction("Packages");
                }

                var totalListings = await _db.Listings.CountAsync(l => l.UserId == customer.Id);
                if (totalListings >= currentPackage.Package.TotalListings)
                {
                    SetErrorMessage("You have reached your listing limit for your current package.");
                    return RedirectToAction("MyListings");
                }

                var brands = await _db.ListingBrands.ToListAsync();
                var locations = await _db.ListingLocations.ToListAsync();
                var amenities = await _db.Amenities.ToListAsync();

                var model = new CustomerAddListingViewModel
                {
                    Brands = brands.Select(b => new SelectListItem { Value = b.Id.ToString(), Text = b.ListingBrandName }).ToList(),
                    Locations = locations.Select(l => new SelectListItem { Value = l.Id.ToString(), Text = l.ListingLocationName }).ToList(),
                    Amenities = amenities.Select(a => new SelectListItem { Value = a.Id.ToString(), Text = a.AmenityName }).ToList(),
                    CurrentPackage = currentPackage,
                    PageOther = await _db.PageOtherItems.FirstOrDefaultAsync() ?? new PageOtherItem()
                };

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading add listing page");
                return RedirectToAction("Dashboard");
            }
        }

        // POST: /Customer/AddListing
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AddListing(CustomerAddListingViewModel model)
        {
            try
            {
                var customer = await GetCurrentCustomerAsync();
                if (customer == null)
                {
                    return RedirectToCustomerLogin();
                }

                // Check if customer has an active package and listing allowance
                var currentPackage = await _db.PackagePurchases
                    .Include(pp => pp.Package)
                    .FirstOrDefaultAsync(pp => pp.UserId == customer.Id && pp.CurrentlyActive == true);

                if (currentPackage == null)
                {
                    SetErrorMessage("You need to purchase a package before adding listings.");
                    return RedirectToAction("Packages");
                }

                var totalListings = await _db.Listings.CountAsync(l => l.UserId == customer.Id);
                if (totalListings >= currentPackage.Package.TotalListings)
                {
                    SetErrorMessage("You have reached your listing limit for your current package.");
                    return RedirectToAction("MyListings");
                }

                // Validate model
                if (!ModelState.IsValid)
                {
                    // Reload dropdown data
                    var brands = await _db.ListingBrands.ToListAsync();
                    var locations = await _db.ListingLocations.ToListAsync();
                    var amenities = await _db.Amenities.ToListAsync();

                    model.Brands = brands.Select(b => new SelectListItem { Value = b.Id.ToString(), Text = b.ListingBrandName }).ToList();
                    model.Locations = locations.Select(l => new SelectListItem { Value = l.Id.ToString(), Text = l.ListingLocationName }).ToList();
                    model.Amenities = amenities.Select(a => new SelectListItem { Value = a.Id.ToString(), Text = a.AmenityName }).ToList();
                    model.CurrentPackage = currentPackage;
                    model.PageOther = await _db.PageOtherItems.FirstOrDefaultAsync() ?? new PageOtherItem();
                    model.ListingErrorMessage = "Please correct the errors below.";

                    return View(model);
                }

                // Create new listing
                var listing = new Listing
                {
                    ListingName = model.ListingName,
                    ListingSlug = model.ListingSlug,
                    ListingDescription = model.ListingDescription,
                    ListingAddress = model.ListingAddress,
                    ListingPhone = model.ListingPhone,
                    ListingEmail = model.ListingEmail,
                    ListingWebsite = model.ListingWebsite,
                    ListingMap = model.ListingMap,
                    ListingPrice = model.ListingPrice,
                    ListingExteriorColor = model.ListingExteriorColor,
                    ListingInteriorColor = model.ListingInteriorColor,
                    ListingModel = model.ListingModel,
                    ListingYear = model.ListingModelYear,
                    ListingMileage = model.ListingMileage,
                    ListingFuelType = model.ListingFuelType,
                    ListingTransmission = model.ListingTransmission,
                    ListingCondition = model.ListingCondition,
                    ListingDriveType = model.ListingDriveType,
                    ListingEngineSize = model.ListingEngineSize,
                    ListingCylinder = model.ListingCylinder,
                    ListingDoor = model.ListingDoor,
                    ListingSeat = model.ListingSeat,
                    ListingVin = model.ListingVin,
                    ListingBrandId = model.ListingBrandId,
                    ListingLocationId = model.ListingLocationId,
                    IsFeatured = false, // Customer listings are not featured by default
                    ListingStatus = "Pending",
                    UserId = customer.Id,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _db.Listings.Add(listing);
                await _db.SaveChangesAsync();

                // Handle featured photo upload
                if (model.ListingFeaturedPhoto != null && model.ListingFeaturedPhoto.Length > 0)
                {
                    var fileName = await SaveListingPhoto(model.ListingFeaturedPhoto, "featured_photos");
                    if (!string.IsNullOrEmpty(fileName))
                    {
                        listing.ListingFeaturedPhoto = fileName;
                        await _db.SaveChangesAsync();
                    }
                }

                // Handle multiple photo uploads
                if (model.ListingPhotos != null)
                {
                    foreach (var photo in model.ListingPhotos)
                    {
                        if (photo.Length > 0)
                        {
                            var fileName = await SaveListingPhoto(photo, "listing_photos");
                            if (!string.IsNullOrEmpty(fileName))
                            {
                                var listingPhoto = new ListingPhoto
                                {
                                    ListingId = listing.Id,
                                    ListingPhotoName = fileName,
                                    CreatedAt = DateTime.UtcNow,
                                    UpdatedAt = DateTime.UtcNow
                                };
                                _db.ListingPhotos.Add(listingPhoto);
                            }
                        }
                    }
                }

                // Handle YouTube video IDs
                if (model.YoutubeVideoIds != null && model.YoutubeVideoIds.Any())
                {
                    foreach (var videoId in model.YoutubeVideoIds.Where(v => !string.IsNullOrWhiteSpace(v)))
                    {
                        var listingVideo = new ListingVideo
                        {
                            ListingId = listing.Id,
                            VideoId = videoId.Trim(),
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow
                        };
                        _db.ListingVideos.Add(listingVideo);
                    }
                }

                // Handle amenities
                if (model.SelectedAmenityIds != null && model.SelectedAmenityIds.Any())
                {
                    foreach (var amenityId in model.SelectedAmenityIds)
                    {
                        var listingAmenity = new ListingAmenity
                        {
                            ListingId = listing.Id,
                            AmenityId = amenityId,
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow
                        };
                        _db.ListingAmenities.Add(listingAmenity);
                    }
                }

                // Handle additional features
                if (model.AdditionalFeatures != null && model.AdditionalFeatures.Any())
                {
                    foreach (var feature in model.AdditionalFeatures)
                    {
                        if (!string.IsNullOrWhiteSpace(feature))
                        {
                            var listingFeature = new ListingAdditionalFeature
                            {
                                ListingId = listing.Id,
                                ListingAdditionalFeatureName = feature.Trim(),
                                CreatedAt = DateTime.UtcNow,
                                UpdatedAt = DateTime.UtcNow
                            };
                            _db.ListingAdditionalFeatures.Add(listingFeature);
                        }
                    }
                }

                // Handle social media items
                if (model.SocialIcons != null && model.SocialUrls != null)
                {
                    for (int i = 0; i < Math.Min(model.SocialIcons.Count, model.SocialUrls.Count); i++)
                    {
                        if (!string.IsNullOrWhiteSpace(model.SocialIcons[i]) && !string.IsNullOrWhiteSpace(model.SocialUrls[i]))
                        {
                            var socialItem = new ListingSocialItem
                            {
                                ListingId = listing.Id,
                                ListingSocialName = model.SocialIcons[i].Trim(),
                                ListingSocialUrl = model.SocialUrls[i].Trim(),
                                CreatedAt = DateTime.UtcNow,
                                UpdatedAt = DateTime.UtcNow
                            };
                            _db.ListingSocialItems.Add(socialItem);
                        }
                    }
                }

                await _db.SaveChangesAsync();

                SetSuccessMessage("Listing added successfully! It will be reviewed before being published.");
                return RedirectToAction("MyListings");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding new listing");
                SetErrorMessage("An error occurred while adding your listing. Please try again.");
                return RedirectToAction("AddListing");
            }
        }

        // GET: /Customer/EditListing/{id}
        public async Task<IActionResult> EditListing(int id)
        {
            try
            {
                var customer = await GetCurrentCustomerAsync();
                if (customer == null)
                {
                    return RedirectToCustomerLogin();
                }

                var listing = await _db.Listings
                    .Include(l => l.ListingBrand)
                    .Include(l => l.ListingLocation)
                    .Include(l => l.ListingPhotos)
                    .Include(l => l.ListingVideos)
                    .Include(l => l.ListingAmenities)
                    .Include(l => l.ListingSocialItems)
                    .Include(l => l.ListingAdditionalFeatures)
                    .FirstOrDefaultAsync(l => l.Id == id && l.UserId == customer.Id);

                if (listing == null)
                {
                    return NotFound();
                }

                var brands = await _db.ListingBrands.ToListAsync();
                var locations = await _db.ListingLocations.ToListAsync();
                var amenities = await _db.Amenities.ToListAsync();

                var model = new CustomerEditListingViewModel
                {
                    Id = listing.Id,
                    ListingName = listing.ListingName,
                    ListingSlug = listing.ListingSlug,
                    ListingDescription = listing.ListingDescription,
                    ListingAddress = listing.ListingAddress,
                    ListingPhone = listing.ListingPhone,
                    ListingEmail = listing.ListingEmail,
                    ListingWebsite = listing.ListingWebsite,
                    ListingMap = listing.ListingMap,
                    ListingPrice = listing.ListingPrice,
                    ListingExteriorColor = listing.ListingExteriorColor,
                    ListingInteriorColor = listing.ListingInteriorColor,
                    ListingModel = listing.ListingModel,
                    ListingModelYear = listing.ListingYear,
                    ListingMileage = listing.ListingMileage,
                    ListingFuelType = listing.ListingFuelType,
                    ListingTransmission = listing.ListingTransmission,
                    ListingCondition = listing.ListingCondition,
                    ListingDriveType = listing.ListingDriveType,
                    ListingEngineSize = listing.ListingEngineSize,
                    ListingCylinder = listing.ListingCylinder,
                    ListingDoor = listing.ListingDoor,
                    ListingSeat = listing.ListingSeat,
                    ListingVin = listing.ListingVin,
                    ListingBrandId = listing.ListingBrandId ?? 0,
                    ListingLocationId = listing.ListingLocationId ?? 0,
                    CurrentFeaturedPhoto = listing.ListingFeaturedPhoto,
                    ExistingPhotos = listing.ListingPhotos.ToList(),
                    ExistingVideos = listing.ListingVideos.ToList(),
                    ExistingSocialItems = listing.ListingSocialItems.ToList(),
                    ExistingAdditionalFeatures = listing.ListingAdditionalFeatures.ToList(),
                    ExistingAmenityIds = listing.ListingAmenities.Select(la => la.AmenityId).ToList(),
                    YoutubeVideoIds = new List<string>(), // Initialize empty list for new videos
                    Brands = brands.Select(b => new SelectListItem { Value = b.Id.ToString(), Text = b.ListingBrandName }).ToList(),
                    Locations = locations.Select(l => new SelectListItem { Value = l.Id.ToString(), Text = l.ListingLocationName }).ToList(),
                    Amenities = amenities.Select(a => new SelectListItem { Value = a.Id.ToString(), Text = a.AmenityName }).ToList(),
                    PageOther = await _db.PageOtherItems.FirstOrDefaultAsync() ?? new PageOtherItem()
                };

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading edit listing page for ID: {ListingId}", id);
                return RedirectToAction("MyListings");
            }
        }

        // POST: /Customer/EditListing/{id}
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditListing(int id, CustomerEditListingViewModel model)
        {
            try
            {
                var customer = await GetCurrentCustomerAsync();
                if (customer == null)
                {
                    return RedirectToCustomerLogin();
                }

                var listing = await _db.Listings
                    .Include(l => l.ListingPhotos)
                    .Include(l => l.ListingVideos)
                    .Include(l => l.ListingAmenities)
                    .Include(l => l.ListingSocialItems)
                    .Include(l => l.ListingAdditionalFeatures)
                    .FirstOrDefaultAsync(l => l.Id == id && l.UserId == customer.Id);

                if (listing == null)
                {
                    return NotFound();
                }

                // Validate model
                if (!ModelState.IsValid)
                {
                    // Reload dropdown data and existing data
                    var brands = await _db.ListingBrands.ToListAsync();
                    var locations = await _db.ListingLocations.ToListAsync();
                    var amenities = await _db.Amenities.ToListAsync();

                    model.Brands = brands.Select(b => new SelectListItem { Value = b.Id.ToString(), Text = b.ListingBrandName }).ToList();
                    model.Locations = locations.Select(l => new SelectListItem { Value = l.Id.ToString(), Text = l.ListingLocationName }).ToList();
                    model.Amenities = amenities.Select(a => new SelectListItem { Value = a.Id.ToString(), Text = a.AmenityName }).ToList();
                    model.ExistingPhotos = listing.ListingPhotos.ToList();
                    model.ExistingVideos = listing.ListingVideos.ToList();
                    model.ExistingSocialItems = listing.ListingSocialItems.ToList();
                    model.ExistingAdditionalFeatures = listing.ListingAdditionalFeatures.ToList();
                    model.ExistingAmenityIds = listing.ListingAmenities.Select(la => la.AmenityId).ToList();
                    model.CurrentFeaturedPhoto = listing.ListingFeaturedPhoto;
                    model.PageOther = await _db.PageOtherItems.FirstOrDefaultAsync() ?? new PageOtherItem();
                    model.ListingErrorMessage = "Please correct the errors below.";

                    return View(model);
                }

                // Update listing properties
                listing.ListingName = model.ListingName;
                listing.ListingSlug = model.ListingSlug;
                listing.ListingDescription = model.ListingDescription;
                listing.ListingAddress = model.ListingAddress;
                listing.ListingPhone = model.ListingPhone;
                listing.ListingEmail = model.ListingEmail;
                listing.ListingWebsite = model.ListingWebsite;
                listing.ListingMap = model.ListingMap;
                listing.ListingPrice = model.ListingPrice;
                listing.ListingExteriorColor = model.ListingExteriorColor;
                listing.ListingInteriorColor = model.ListingInteriorColor;
                listing.ListingModel = model.ListingModel;
                listing.ListingYear = model.ListingModelYear;
                listing.ListingMileage = model.ListingMileage;
                listing.ListingFuelType = model.ListingFuelType;
                listing.ListingTransmission = model.ListingTransmission;
                listing.ListingCondition = model.ListingCondition;
                listing.ListingDriveType = model.ListingDriveType;
                listing.ListingEngineSize = model.ListingEngineSize;
                listing.ListingCylinder = model.ListingCylinder;
                listing.ListingDoor = model.ListingDoor;
                listing.ListingSeat = model.ListingSeat;
                listing.ListingVin = model.ListingVin;
                listing.ListingBrandId = model.ListingBrandId;
                listing.ListingLocationId = model.ListingLocationId;
                listing.UpdatedAt = DateTime.UtcNow;

                // Handle featured photo upload
                if (model.ListingFeaturedPhoto != null && model.ListingFeaturedPhoto.Length > 0)
                {
                    // Delete old featured photo if exists
                    if (!string.IsNullOrEmpty(listing.ListingFeaturedPhoto))
                    {
                        DeleteListingPhoto(listing.ListingFeaturedPhoto, "featured_photos");
                    }

                    var fileName = await SaveListingPhoto(model.ListingFeaturedPhoto, "featured_photos");
                    if (!string.IsNullOrEmpty(fileName))
                    {
                        listing.ListingFeaturedPhoto = fileName;
                    }
                }

                // Handle photo deletions
                if (!string.IsNullOrEmpty(model.PhotosToDelete))
                {
                    var photoIds = model.PhotosToDelete.Split(',', StringSplitOptions.RemoveEmptyEntries)
                        .Select(id => int.TryParse(id.Trim(), out var result) ? result : 0)
                        .Where(id => id > 0);

                    foreach (var photoId in photoIds)
                    {
                        var photoToDelete = listing.ListingPhotos.FirstOrDefault(p => p.Id == photoId);
                        if (photoToDelete != null)
                        {
                            DeleteListingPhoto(photoToDelete.ListingPhotoName, "listing_photos");
                            _db.ListingPhotos.Remove(photoToDelete);
                        }
                    }
                }

                // Handle new photo uploads
                if (model.ListingPhotos != null)
                {
                    foreach (var photo in model.ListingPhotos)
                    {
                        if (photo.Length > 0)
                        {
                            var fileName = await SaveListingPhoto(photo, "listing_photos");
                            if (!string.IsNullOrEmpty(fileName))
                            {
                                var listingPhoto = new ListingPhoto
                                {
                                    ListingId = listing.Id,
                                    ListingPhotoName = fileName,
                                    CreatedAt = DateTime.UtcNow,
                                    UpdatedAt = DateTime.UtcNow
                                };
                                _db.ListingPhotos.Add(listingPhoto);
                            }
                        }
                    }
                }

                // Handle video deletions
                if (!string.IsNullOrEmpty(model.VideosToDelete))
                {
                    var videoIds = model.VideosToDelete.Split(',', StringSplitOptions.RemoveEmptyEntries)
                        .Select(id => int.TryParse(id.Trim(), out var result) ? result : 0)
                        .Where(id => id > 0);

                    foreach (var videoId in videoIds)
                    {
                        var videoToDelete = listing.ListingVideos.FirstOrDefault(v => v.Id == videoId);
                        if (videoToDelete != null)
                        {
                            _db.ListingVideos.Remove(videoToDelete);
                        }
                    }
                }

                // Handle new YouTube video IDs
                if (model.YoutubeVideoIds != null && model.YoutubeVideoIds.Any())
                {
                    foreach (var videoId in model.YoutubeVideoIds.Where(v => !string.IsNullOrWhiteSpace(v)))
                    {
                        var listingVideo = new ListingVideo
                        {
                            ListingId = listing.Id,
                            VideoId = videoId.Trim(),
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow
                        };
                        _db.ListingVideos.Add(listingVideo);
                    }
                }

                // Update amenities
                _db.ListingAmenities.RemoveRange(listing.ListingAmenities);
                if (model.SelectedAmenityIds != null && model.SelectedAmenityIds.Any())
                {
                    foreach (var amenityId in model.SelectedAmenityIds)
                    {
                        var listingAmenity = new ListingAmenity
                        {
                            ListingId = listing.Id,
                            AmenityId = amenityId,
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow
                        };
                        _db.ListingAmenities.Add(listingAmenity);
                    }
                }

                // Update additional features
                _db.ListingAdditionalFeatures.RemoveRange(listing.ListingAdditionalFeatures);
                if (model.AdditionalFeatures != null && model.AdditionalFeatures.Any())
                {
                    foreach (var feature in model.AdditionalFeatures)
                    {
                        if (!string.IsNullOrWhiteSpace(feature))
                        {
                            var listingFeature = new ListingAdditionalFeature
                            {
                                ListingId = listing.Id,
                                ListingAdditionalFeatureName = feature.Trim(),
                                CreatedAt = DateTime.UtcNow,
                                UpdatedAt = DateTime.UtcNow
                            };
                            _db.ListingAdditionalFeatures.Add(listingFeature);
                        }
                    }
                }

                // Update social media items
                _db.ListingSocialItems.RemoveRange(listing.ListingSocialItems);
                if (model.SocialIcons != null && model.SocialUrls != null)
                {
                    for (int i = 0; i < Math.Min(model.SocialIcons.Count, model.SocialUrls.Count); i++)
                    {
                        if (!string.IsNullOrWhiteSpace(model.SocialIcons[i]) && !string.IsNullOrWhiteSpace(model.SocialUrls[i]))
                        {
                            var socialItem = new ListingSocialItem
                            {
                                ListingId = listing.Id,
                                ListingSocialName = model.SocialIcons[i].Trim(),
                                ListingSocialUrl = model.SocialUrls[i].Trim(),
                                CreatedAt = DateTime.UtcNow,
                                UpdatedAt = DateTime.UtcNow
                            };
                            _db.ListingSocialItems.Add(socialItem);
                        }
                    }
                }

                await _db.SaveChangesAsync();

                SetSuccessMessage("Listing updated successfully!");
                return RedirectToAction("EditListing", new { id = listing.Id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating listing with ID: {ListingId}", id);
                SetErrorMessage("An error occurred while updating your listing. Please try again.");
                return RedirectToAction("EditListing", new { id });
            }
        }

        // GET: /Customer/EditProfile
        public async Task<IActionResult> EditProfile()
        {
            try
            {
                var customerId = GetCurrentCustomerId();
                if (customerId == null)
                {
                    return RedirectToAction("Login", "Account", new { Area = "Customer" });
                }

                var customer = await _db.Users.FirstOrDefaultAsync(u => u.Id == customerId.Value);
                if (customer == null)
                {
                    return RedirectToAction("Login", "Account", new { Area = "Customer" });
                }

                var model = new CustomerEditProfileViewModel
                {
                    Customer = customer,
                    PageOther = await _db.PageOtherItems.FirstOrDefaultAsync() ?? new PageOtherItem()
                };

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading edit profile page");
                return RedirectToAction("Dashboard");
            }
        }

        // POST: /Customer/EditProfile
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditProfile(CustomerEditProfileViewModel model)
        {
            try
            {
                var customerId = GetCurrentCustomerId();
                if (customerId == null)
                {
                    return RedirectToAction("Login", "Account", new { Area = "Customer" });
                }

                if (!ModelState.IsValid)
                {
                    model.PageOther = await _db.PageOtherItems.FirstOrDefaultAsync() ?? new PageOtherItem();
                    return View(model);
                }

                var customer = await _db.Users.FirstOrDefaultAsync(u => u.Id == customerId.Value);
                if (customer == null)
                {
                    SetErrorMessage("Customer not found.");
                    return RedirectToAction("Dashboard");
                }

                // Update customer profile information
                customer.Name = model.Customer.Name;
                customer.Email = model.Customer.Email;
                customer.Phone = model.Customer.Phone;
                customer.Country = model.Customer.Country;
                customer.Address = model.Customer.Address;
                customer.State = model.Customer.State;
                customer.City = model.Customer.City;
                customer.ZipCode = model.Customer.ZipCode;
                customer.Website = model.Customer.Website;
                customer.Bio = model.Customer.Bio;
                customer.UpdatedAt = DateTime.UtcNow;

                await _db.SaveChangesAsync();

                SetSuccessMessage("Profile updated successfully!");
                return RedirectToAction("EditProfile");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating customer profile");
                SetErrorMessage("An error occurred while updating your profile.");
                model.PageOther = await _db.PageOtherItems.FirstOrDefaultAsync() ?? new PageOtherItem();
                return View(model);
            }
        }

        // GET: /Customer/MyReviews
        public async Task<IActionResult> MyReviews(int? pageIndex)
        {
            try
            {
                var customerId = GetCurrentCustomerId();
                if (customerId == null)
                {
                    return RedirectToAction("Login", "Account", new { Area = "Customer" });
                }

                var query = _db.Reviews
                    .Include(r => r.Listing)
                    .Where(r => r.AgentId == customerId.Value && r.AgentType == "User")
                    .OrderByDescending(r => r.CreatedAt);

                int pageSize = 10;
                var reviews = await PaginatedList<Review>.CreateAsync(query, pageIndex ?? 1, pageSize);

                var model = new CustomerMyReviewsViewModel
                {
                    Reviews = reviews,
                    PageOther = await _db.PageOtherItems.FirstOrDefaultAsync() ?? new PageOtherItem()
                };

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading customer reviews");
                return View(new CustomerMyReviewsViewModel());
            }
        }

        // GET: /Customer/Wishlist
        public async Task<IActionResult> Wishlist(int? pageIndex)
        {
            try
            {
                var customerId = GetCurrentCustomerId();
                if (customerId == null)
                {
                    return RedirectToAction("Login", "Account", new { Area = "Customer" });
                }

                var query = _db.Wishlists
                    .Include(w => w.Listing)
                        .ThenInclude(l => l.ListingBrand)
                    .Include(w => w.Listing)
                        .ThenInclude(l => l.ListingLocation)
                    .Where(w => w.UserId == customerId.Value)
                    .OrderByDescending(w => w.CreatedAt);

                int pageSize = 10;
                var wishlistItems = await PaginatedList<Wishlist>.CreateAsync(query, pageIndex ?? 1, pageSize);

                var model = new CustomerWishlistViewModel
                {
                    WishlistItems = wishlistItems,
                    PageOther = await _db.PageOtherItems.FirstOrDefaultAsync() ?? new PageOtherItem()
                };

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading customer wishlist");
                return View(new CustomerWishlistViewModel());
            }
        }

        // GET: /Customer/EditPassword
        public async Task<IActionResult> EditPassword()
        {
            try
            {
                var customerId = GetCurrentCustomerId();
                if (customerId == null)
                {
                    return RedirectToAction("Login", "Account", new { Area = "Customer" });
                }

                var model = new CustomerEditPasswordViewModel
                {
                    PageOtherItem = await _db.PageOtherItems.FirstOrDefaultAsync() ?? new PageOtherItem()
                };

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading edit password page");
                return RedirectToAction("Dashboard");
            }
        }

        // POST: /Customer/EditPassword
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditPassword(CustomerEditPasswordViewModel model)
        {
            try
            {
                var customerId = GetCurrentCustomerId();
                if (customerId == null)
                {
                    return RedirectToAction("Login", "Account", new { Area = "Customer" });
                }

                if (!ModelState.IsValid)
                {
                    model.PageOtherItem = await _db.PageOtherItems.FirstOrDefaultAsync() ?? new PageOtherItem();
                    return View(model);
                }

                var customer = await _db.Users.FirstOrDefaultAsync(u => u.Id == customerId.Value);
                if (customer == null)
                {
                    SetErrorMessage("Customer not found.");
                    return RedirectToAction("Dashboard");
                }

                // Verify current password
                if (!BCrypt.Net.BCrypt.Verify(model.CurrentPassword, customer.Password))
                {
                    ModelState.AddModelError("CurrentPassword", "Current password is incorrect.");
                    model.PageOtherItem = await _db.PageOtherItems.FirstOrDefaultAsync() ?? new PageOtherItem();
                    return View(model);
                }

                // Hash the new password
                customer.Password = BCrypt.Net.BCrypt.HashPassword(model.Password);
                customer.UpdatedAt = DateTime.UtcNow;

                await _db.SaveChangesAsync();

                SetSuccessMessage("Password updated successfully!");
                return RedirectToAction("Dashboard");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating customer password");
                SetErrorMessage("An error occurred while updating your password.");
                model.PageOtherItem = await _db.PageOtherItems.FirstOrDefaultAsync() ?? new PageOtherItem();
                return View(model);
            }
        }

        // GET: /Customer/EditPhoto
        public async Task<IActionResult> EditPhoto()
        {
            try
            {
                var customerId = GetCurrentCustomerId();
                if (customerId == null)
                {
                    return RedirectToAction("Login", "Account", new { Area = "Customer" });
                }

                var customer = await _db.Users.FirstOrDefaultAsync(u => u.Id == customerId.Value);
                if (customer == null)
                {
                    return RedirectToAction("Login", "Account", new { Area = "Customer" });
                }

                var model = new CustomerEditPhotoViewModel
                {
                    Customer = customer,
                    PageOtherItem = await _db.PageOtherItems.FirstOrDefaultAsync() ?? new PageOtherItem()
                };

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading edit photo page");
                return RedirectToAction("Dashboard");
            }
        }

        // POST: /Customer/EditPhoto
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditPhoto(IFormFile? photo)
        {
            try
            {
                var customerId = GetCurrentCustomerId();
                if (customerId == null)
                {
                    return RedirectToAction("Login", "Account", new { Area = "Customer" });
                }

                var customer = await _db.Users.FirstOrDefaultAsync(u => u.Id == customerId.Value);
                if (customer == null)
                {
                    SetErrorMessage("Customer not found.");
                    return RedirectToAction("Dashboard");
                }

                if (photo != null && photo.Length > 0)
                {
                    // Validate file type
                    var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif" };
                    var fileExtension = Path.GetExtension(photo.FileName).ToLowerInvariant();

                    if (!allowedExtensions.Contains(fileExtension))
                    {
                        SetErrorMessage("Please upload a valid image file (JPG, JPEG, PNG, GIF).");
                        return RedirectToAction("EditPhoto");
                    }

                    // Validate file size (max 5MB)
                    if (photo.Length > 5 * 1024 * 1024)
                    {
                        SetErrorMessage("File size must be less than 5MB.");
                        return RedirectToAction("EditPhoto");
                    }

                    // Delete old photo if exists
                    if (!string.IsNullOrEmpty(customer.Photo))
                    {
                        var oldPhotoPath = Path.Combine("wwwroot", "uploads", "user_photos", customer.Photo);
                        if (System.IO.File.Exists(oldPhotoPath))
                        {
                            System.IO.File.Delete(oldPhotoPath);
                        }
                    }

                    // Save new photo
                    var fileName = $"{Guid.NewGuid()}{fileExtension}";
                    var uploadPath = Path.Combine("wwwroot", "uploads", "user_photos");

                    if (!Directory.Exists(uploadPath))
                    {
                        Directory.CreateDirectory(uploadPath);
                    }

                    var filePath = Path.Combine(uploadPath, fileName);
                    using (var stream = new FileStream(filePath, FileMode.Create))
                    {
                        await photo.CopyToAsync(stream);
                    }

                    customer.Photo = fileName;
                    customer.UpdatedAt = DateTime.UtcNow;

                    await _db.SaveChangesAsync();

                    SetSuccessMessage("Photo updated successfully!");
                }
                else
                {
                    SetErrorMessage("Please select a photo to upload.");
                }

                return RedirectToAction("EditPhoto");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating customer photo");
                SetErrorMessage("An error occurred while updating your photo.");
                return RedirectToAction("EditPhoto");
            }
        }

        // GET: /Customer/EditBanner
        public async Task<IActionResult> EditBanner()
        {
            try
            {
                var customerId = GetCurrentCustomerId();
                if (customerId == null)
                {
                    return RedirectToAction("Login", "Account", new { Area = "Customer" });
                }

                var customer = await _db.Users.FirstOrDefaultAsync(u => u.Id == customerId.Value);
                if (customer == null)
                {
                    return RedirectToAction("Login", "Account", new { Area = "Customer" });
                }

                var model = new CustomerEditBannerViewModel
                {
                    Customer = customer,
                    PageOtherItem = await _db.PageOtherItems.FirstOrDefaultAsync() ?? new PageOtherItem()
                };

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading edit banner page");
                return RedirectToAction("Dashboard");
            }
        }

        // POST: /Customer/EditBanner
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditBanner(IFormFile? banner)
        {
            try
            {
                var customerId = GetCurrentCustomerId();
                if (customerId == null)
                {
                    return RedirectToAction("Login", "Account", new { Area = "Customer" });
                }

                var customer = await _db.Users.FirstOrDefaultAsync(u => u.Id == customerId.Value);
                if (customer == null)
                {
                    SetErrorMessage("Customer not found.");
                    return RedirectToAction("Dashboard");
                }

                if (banner != null && banner.Length > 0)
                {
                    // Validate file type
                    var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif" };
                    var fileExtension = Path.GetExtension(banner.FileName).ToLowerInvariant();

                    if (!allowedExtensions.Contains(fileExtension))
                    {
                        SetErrorMessage("Please upload a valid image file (JPG, JPEG, PNG, GIF).");
                        return RedirectToAction("EditBanner");
                    }

                    // Validate file size (max 10MB for banner)
                    if (banner.Length > 10 * 1024 * 1024)
                    {
                        SetErrorMessage("File size must be less than 10MB.");
                        return RedirectToAction("EditBanner");
                    }

                    // Delete old banner if exists
                    if (!string.IsNullOrEmpty(customer.Banner))
                    {
                        var oldBannerPath = Path.Combine("wwwroot", "uploads", "user_banners", customer.Banner);
                        if (System.IO.File.Exists(oldBannerPath))
                        {
                            System.IO.File.Delete(oldBannerPath);
                        }
                    }

                    // Save new banner
                    var fileName = $"{Guid.NewGuid()}{fileExtension}";
                    var uploadPath = Path.Combine("wwwroot", "uploads", "user_banners");

                    if (!Directory.Exists(uploadPath))
                    {
                        Directory.CreateDirectory(uploadPath);
                    }

                    var filePath = Path.Combine(uploadPath, fileName);
                    using (var stream = new FileStream(filePath, FileMode.Create))
                    {
                        await banner.CopyToAsync(stream);
                    }

                    customer.Banner = fileName;
                    customer.UpdatedAt = DateTime.UtcNow;

                    await _db.SaveChangesAsync();

                    SetSuccessMessage("Banner updated successfully!");
                }
                else
                {
                    SetErrorMessage("Please select a banner image to upload.");
                }

                return RedirectToAction("EditBanner");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating customer banner");
                SetErrorMessage("An error occurred while updating your banner.");
                return RedirectToAction("EditBanner");
            }
        }

        // GET: /Customer/EditReview/{id}
        public async Task<IActionResult> EditReview(int id)
        {
            try
            {
                var customerId = GetCurrentCustomerId();
                if (customerId == null)
                {
                    return RedirectToAction("Login", "Account", new { Area = "Customer" });
                }

                var review = await _db.Reviews
                    .Include(r => r.Listing)
                    .FirstOrDefaultAsync(r => r.Id == id && r.AgentId == customerId.Value && r.AgentType == "User");

                if (review == null)
                {
                    SetErrorMessage("Review not found or you don't have permission to edit it.");
                    return RedirectToAction("MyReviews");
                }

                var model = new CustomerEditReviewViewModel
                {
                    Review = review,
                    PageOtherItem = await _db.PageOtherItems.FirstOrDefaultAsync() ?? new PageOtherItem()
                };

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading edit review page for ID: {ReviewId}", id);
                return RedirectToAction("MyReviews");
            }
        }

        // POST: /Customer/EditReview/{id}
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditReview(int id, CustomerEditReviewViewModel model)
        {
            try
            {
                var customerId = GetCurrentCustomerId();
                if (customerId == null)
                {
                    return RedirectToAction("Login", "Account", new { Area = "Customer" });
                }

                var review = await _db.Reviews.FirstOrDefaultAsync(r => r.Id == id && r.AgentId == customerId.Value && r.AgentType == "User");
                if (review == null)
                {
                    SetErrorMessage("Review not found or you don't have permission to edit it.");
                    return RedirectToAction("MyReviews");
                }

                if (!ModelState.IsValid)
                {
                    model.PageOtherItem = await _db.PageOtherItems.FirstOrDefaultAsync() ?? new PageOtherItem();
                    return View(model);
                }

                // Update review
                review.Rating = model.Review.Rating;
                review.ReviewText = model.Review.ReviewText;
                review.UpdatedAt = DateTime.UtcNow;

                await _db.SaveChangesAsync();

                SetSuccessMessage("Review updated successfully!");
                return RedirectToAction("MyReviews");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating review ID: {ReviewId}", id);
                SetErrorMessage("An error occurred while updating your review.");
                model.PageOtherItem = await _db.PageOtherItems.FirstOrDefaultAsync() ?? new PageOtherItem();
                return View(model);
            }
        }

        // GET: /Customer/Invoice/{id}
        public async Task<IActionResult> Invoice(int id)
        {
            try
            {
                var customerId = GetCurrentCustomerId();
                if (customerId == null)
                {
                    return RedirectToAction("Login", "Account", new { Area = "Customer" });
                }

                var purchase = await _db.PackagePurchases
                    .Include(pp => pp.Package)
                    .FirstOrDefaultAsync(pp => pp.Id == id && pp.UserId == customerId.Value);

                if (purchase == null)
                {
                    SetErrorMessage("Invoice not found or you don't have permission to view it.");
                    return RedirectToAction("PurchaseHistory");
                }

                var customer = await _db.Users.FirstOrDefaultAsync(u => u.Id == customerId.Value);
                if (customer == null)
                {
                    return RedirectToAction("Login", "Account", new { Area = "Customer" });
                }

                var model = new CustomerInvoiceViewModel
                {
                    PackagePurchase = purchase,
                    Customer = customer,
                    GeneralSettings = await _db.GeneralSettings.FirstOrDefaultAsync(),
                    PageOtherItem = await _db.PageOtherItems.FirstOrDefaultAsync() ?? new PageOtherItem()
                };

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading invoice for ID: {InvoiceId}", id);
                return RedirectToAction("PurchaseHistory");
            }
        }

        // GET: /Customer/PurchaseHistoryDetail/{id}
        public async Task<IActionResult> PurchaseHistoryDetail(int id)
        {
            try
            {
                var customerId = GetCurrentCustomerId();
                if (customerId == null)
                {
                    return RedirectToAction("Login", "Account", new { Area = "Customer" });
                }

                var purchase = await _db.PackagePurchases
                    .Include(pp => pp.Package)
                    .FirstOrDefaultAsync(pp => pp.Id == id && pp.UserId == customerId.Value);

                if (purchase == null)
                {
                    SetErrorMessage("Purchase record not found or you don't have permission to view it.");
                    return RedirectToAction("PurchaseHistory");
                }

                var model = new CustomerPurchaseHistoryDetailViewModel
                {
                    PackagePurchase = purchase,
                    PageOtherItem = await _db.PageOtherItems.FirstOrDefaultAsync() ?? new PageOtherItem()
                };

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading purchase history detail for ID: {PurchaseId}", id);
                return RedirectToAction("PurchaseHistory");
            }
        }

        // GET: /Customer/ViewListingDetail/{id}
        public async Task<IActionResult> ViewListingDetail(int id)
        {
            try
            {
                var customerId = GetCurrentCustomerId();
                if (customerId == null)
                {
                    return RedirectToAction("Login", "Account", new { Area = "Customer" });
                }

                var listing = await _db.Listings
                    .Include(l => l.ListingBrand)
                    .Include(l => l.ListingLocation)
                    .FirstOrDefaultAsync(l => l.Id == id && l.UserId == customerId.Value);

                if (listing == null)
                {
                    SetErrorMessage("Listing not found or you don't have permission to view it.");
                    return RedirectToAction("MyListings");
                }

                // Get related data
                var listingPhotos = await _db.ListingPhotos.Where(lp => lp.ListingId == id).ToListAsync();
                var listingVideos = await _db.ListingVideos.Where(lv => lv.ListingId == id).ToListAsync();
                var listingSocialItems = await _db.ListingSocialItems.Where(lsi => lsi.ListingId == id).ToListAsync();
                var listingAdditionalFeatures = await _db.ListingAdditionalFeatures.Where(laf => laf.ListingId == id).ToListAsync();
                var amenities = await _db.Amenities.ToListAsync();

                // Get existing amenities for this listing
                var existingAmenities = await _db.ListingAmenities
                    .Where(la => la.ListingId == id)
                    .Select(la => la.AmenityId)
                    .ToListAsync();

                var model = new CustomerViewListingDetailViewModel
                {
                    Listing = listing,
                    ListingPhotos = listingPhotos,
                    ListingVideos = listingVideos,
                    ListingSocialItems = listingSocialItems,
                    ListingAdditionalFeatures = listingAdditionalFeatures,
                    Amenities = amenities,
                    ExistingAmenities = existingAmenities,
                    PageOtherItem = await _db.PageOtherItems.FirstOrDefaultAsync() ?? new PageOtherItem()
                };

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading listing detail for ID: {ListingId}", id);
                return RedirectToAction("MyListings");
            }
        }

        // GET: /Customer/BuyPackage/{id}
        public async Task<IActionResult> BuyPackage(int id)
        {
            try
            {
                var customer = await GetCurrentCustomerAsync();
                if (customer == null)
                {
                    return RedirectToCustomerLogin();
                }

                var package = await _db.Packages.FirstOrDefaultAsync(p => p.Id == id);
                if (package == null)
                {
                    SetErrorMessage("Package not found.");
                    return RedirectToAction("Packages");
                }

                // Check if package is free
                if (package.PackageType == PackageType.Free)
                {
                    return RedirectToAction("FreeEnroll", new { id = id });
                }

                // Get current currency from session or default
                var currentCurrency = await _currencyService.GetCurrentCurrencyAsync();
                var generalSettings = await _db.GeneralSettings.FirstOrDefaultAsync();
                var pageOtherItem = await _db.PageOtherItems.FirstOrDefaultAsync();

                // Calculate final price with currency conversion
                var finalPrice = package.PackagePrice * currentCurrency.CurrencyValue;

                // Store package information in session for payment processing
                HttpContext.Session.SetString("PackageId", package.Id.ToString());
                HttpContext.Session.SetString("PackageName", package.PackageName);
                HttpContext.Session.SetString("PackagePrice", package.PackagePrice.ToString());
                HttpContext.Session.SetString("CurrencyName", currentCurrency.CurrencyName);
                HttpContext.Session.SetString("CurrencySymbol", currentCurrency.CurrencySymbol);
                HttpContext.Session.SetString("CurrencyValue", currentCurrency.CurrencyValue.ToString());

                var model = new CustomerPackageBuyViewModel
                {
                    PackageId = package.Id,
                    PackageName = package.PackageName,
                    PackagePrice = package.PackagePrice,
                    FinalPrice = Math.Round(finalPrice, 2),
                    CurrentCurrencyName = currentCurrency.CurrencyName,
                    CurrentCurrencySymbol = currentCurrency.CurrencySymbol,
                    CurrentCurrencyValue = currentCurrency.CurrencyValue,
                    CustomerEmail = customer.Email ?? "",
                    CustomerName = customer.Name ?? "",
                    CustomerPhone = customer.Phone ?? "",
                    GeneralSettings = generalSettings,
                    PageOtherItem = pageOtherItem
                };

                return View("PackageBuy", model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading package buy page for package ID: {PackageId}", id);
                SetErrorMessage("An error occurred while loading the payment page.");
                return RedirectToAction("Packages");
            }
        }

        // GET: /Customer/FreeEnroll/{id}
        public async Task<IActionResult> FreeEnroll(int id)
        {
            try
            {
                var customer = await GetCurrentCustomerAsync();
                if (customer == null)
                {
                    return RedirectToCustomerLogin();
                }

                var package = await _db.Packages.FirstOrDefaultAsync(p => p.Id == id);
                if (package == null || package.PackageType != PackageType.Free)
                {
                    SetErrorMessage("Free package not found.");
                    return RedirectToAction("Packages");
                }

                // Check if user already has an active package
                var existingPurchase = await _db.PackagePurchases
                    .FirstOrDefaultAsync(pp => pp.UserId == customer.Id && pp.CurrentlyActive == true);

                if (existingPurchase != null)
                {
                    SetErrorMessage("You already have an active package. Please wait for it to expire before enrolling in a new one.");
                    return RedirectToAction("Packages");
                }

                // Create free package purchase
                var purchase = new PackagePurchase
                {
                    UserId = customer.Id,
                    PackageId = package.Id,
                    PackageStartDate = DateTime.UtcNow,
                    PackageEndDate = DateTime.UtcNow.AddDays(package.ValidDays),
                    PaymentMethod = "Free",
                    PaymentStatus = "Completed",
                    PaidAmount = 0,
                    PaidCurrencyName = "USD",
                    PaidCurrencySymbol = "$",
                    TransactionId = _paymentService.GenerateTransactionId(),
                    CurrentlyActive = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _db.PackagePurchases.Add(purchase);
                await _db.SaveChangesAsync();

                SetSuccessMessage("You have successfully enrolled in the free package!");
                return RedirectToAction("PurchaseHistory");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error enrolling in free package ID: {PackageId}", id);
                SetErrorMessage("An error occurred while enrolling in the free package.");
                return RedirectToAction("Packages");
            }
        }

        // POST: /Customer/PaymentStripe
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> PaymentStripe()
        {
            try
            {
                var customer = await GetCurrentCustomerAsync();
                if (customer == null)
                {
                    return RedirectToCustomerLogin();
                }

                // Get package information from session
                var packageIdStr = HttpContext.Session.GetString("PackageId");
                var packagePriceStr = HttpContext.Session.GetString("PackagePrice");
                var currencyValueStr = HttpContext.Session.GetString("CurrencyValue");
                var currencyName = HttpContext.Session.GetString("CurrencyName");

                if (string.IsNullOrEmpty(packageIdStr) || string.IsNullOrEmpty(packagePriceStr))
                {
                    SetErrorMessage("Session expired. Please try again.");
                    return RedirectToAction("Packages");
                }

                if (!int.TryParse(packageIdStr, out var packageId) ||
                    !decimal.TryParse(packagePriceStr, out var packagePrice) ||
                    !decimal.TryParse(currencyValueStr, out var currencyValue))
                {
                    SetErrorMessage("Invalid package information.");
                    return RedirectToAction("Packages");
                }

                var package = await _db.Packages.FirstOrDefaultAsync(p => p.Id == packageId);
                if (package == null)
                {
                    SetErrorMessage("Package not found.");
                    return RedirectToAction("Packages");
                }

                // Calculate final amount
                var finalAmount = packagePrice * currencyValue;

                // Get Stripe token from form
                var stripeToken = Request.Form["stripeToken"];
                if (string.IsNullOrEmpty(stripeToken))
                {
                    SetErrorMessage("Payment token is missing.");
                    return RedirectToAction("BuyPackage", new { id = packageId });
                }

                // Process payment with Stripe
                var metadata = new Dictionary<string, string>
                {
                    { "userId", customer.Id.ToString() },
                    { "packageId", packageId.ToString() },
                    { "customerEmail", customer.Email ?? "" }
                };

                var paymentIntentSecret = await _paymentService.CreateStripePaymentIntentAsync(
                    finalAmount,
                    currencyName?.ToLower() ?? "usd",
                    $"Package Purchase: {package.PackageName}",
                    metadata
                );

                // For now, simulate successful payment
                // In a real implementation, you would handle the payment confirmation
                var transactionId = _paymentService.GenerateTransactionId();

                await ProcessSuccessfulPayment(customer.Id, packageId, "Stripe", finalAmount, transactionId);

                SetSuccessMessage("Payment successful! Your package has been activated.");
                return RedirectToAction("PurchaseHistory");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing Stripe payment");
                SetErrorMessage("Payment failed. Please try again.");
                return RedirectToAction("Packages");
            }
        }

        // GET: /Customer/PaymentPaypal
        public async Task<IActionResult> PaymentPaypal()
        {
            try
            {
                var customer = await GetCurrentCustomerAsync();
                if (customer == null)
                {
                    return RedirectToCustomerLogin();
                }

                // Get PayPal payment details from query parameters
                var paymentId = Request.Query["paymentId"];
                var payerId = Request.Query["PayerID"];

                if (string.IsNullOrEmpty(paymentId) || string.IsNullOrEmpty(payerId))
                {
                    SetErrorMessage("Invalid PayPal payment information.");
                    return RedirectToAction("Packages");
                }

                // Get package information from session
                var packageIdStr = HttpContext.Session.GetString("PackageId");
                var packagePriceStr = HttpContext.Session.GetString("PackagePrice");
                var currencyValueStr = HttpContext.Session.GetString("CurrencyValue");

                if (string.IsNullOrEmpty(packageIdStr) || string.IsNullOrEmpty(packagePriceStr))
                {
                    SetErrorMessage("Session expired. Please try again.");
                    return RedirectToAction("Packages");
                }

                if (!int.TryParse(packageIdStr, out var packageId) ||
                    !decimal.TryParse(packagePriceStr, out var packagePrice) ||
                    !decimal.TryParse(currencyValueStr, out var currencyValue))
                {
                    SetErrorMessage("Invalid package information.");
                    return RedirectToAction("Packages");
                }

                // Execute PayPal payment
                var paymentSuccess = await _paymentService.ExecutePayPalPaymentAsync(paymentId, payerId);

                if (paymentSuccess)
                {
                    var finalAmount = packagePrice * currencyValue;
                    await ProcessSuccessfulPayment(customer.Id, packageId, "PayPal", finalAmount, paymentId);

                    SetSuccessMessage("PayPal payment successful! Your package has been activated.");
                    return RedirectToAction("PurchaseHistory");
                }
                else
                {
                    SetErrorMessage("PayPal payment failed. Please try again.");
                    return RedirectToAction("BuyPackage", new { id = packageId });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing PayPal payment");
                SetErrorMessage("Payment failed. Please try again.");
                return RedirectToAction("Packages");
            }
        }

        // POST: /Customer/PaymentRazorpay
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> PaymentRazorpay()
        {
            try
            {
                var customer = await GetCurrentCustomerAsync();
                if (customer == null)
                {
                    return RedirectToCustomerLogin();
                }

                // Get Razorpay payment details from form
                var razorpayPaymentId = Request.Form["razorpay_payment_id"];
                var razorpayOrderId = Request.Form["razorpay_order_id"];
                var razorpaySignature = Request.Form["razorpay_signature"];

                if (string.IsNullOrEmpty(razorpayPaymentId))
                {
                    SetErrorMessage("Razorpay payment information is missing.");
                    return RedirectToAction("Packages");
                }

                // Get package information from session
                var packageIdStr = HttpContext.Session.GetString("PackageId");
                var packagePriceStr = HttpContext.Session.GetString("PackagePrice");
                var currencyValueStr = HttpContext.Session.GetString("CurrencyValue");

                if (string.IsNullOrEmpty(packageIdStr) || string.IsNullOrEmpty(packagePriceStr))
                {
                    SetErrorMessage("Session expired. Please try again.");
                    return RedirectToAction("Packages");
                }

                if (!int.TryParse(packageIdStr, out var packageId) ||
                    !decimal.TryParse(packagePriceStr, out var packagePrice) ||
                    !decimal.TryParse(currencyValueStr, out var currencyValue))
                {
                    SetErrorMessage("Invalid package information.");
                    return RedirectToAction("Packages");
                }

                // Process Razorpay payment (simplified for demo)
                var finalAmount = packagePrice * currencyValue;
                await ProcessSuccessfulPayment(customer.Id, packageId, "Razorpay", finalAmount, razorpayPaymentId);

                SetSuccessMessage("Razorpay payment successful! Your package has been activated.");
                return RedirectToAction("PurchaseHistory");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing Razorpay payment");
                SetErrorMessage("Payment failed. Please try again.");
                return RedirectToAction("Packages");
            }
        }

        // POST: /Customer/PaymentFlutterwave
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> PaymentFlutterwave()
        {
            try
            {
                var customer = await GetCurrentCustomerAsync();
                if (customer == null)
                {
                    return Json(new { success = false, message = "User not authenticated" });
                }

                // Get Flutterwave transaction ID from form
                var transactionId = Request.Form["tnx_id"];
                var packageIdStr = Request.Form["package_id"];

                if (string.IsNullOrEmpty(transactionId) || string.IsNullOrEmpty(packageIdStr))
                {
                    return Json(new { success = false, message = "Missing payment information" });
                }

                if (!int.TryParse(packageIdStr, out var packageId))
                {
                    return Json(new { success = false, message = "Invalid package information" });
                }

                // Get package information from session
                var packagePriceStr = HttpContext.Session.GetString("PackagePrice");
                var currencyValueStr = HttpContext.Session.GetString("CurrencyValue");

                if (string.IsNullOrEmpty(packagePriceStr) ||
                    !decimal.TryParse(packagePriceStr, out var packagePrice) ||
                    !decimal.TryParse(currencyValueStr, out var currencyValue))
                {
                    return Json(new { success = false, message = "Session expired" });
                }

                // Process Flutterwave payment (simplified for demo)
                var finalAmount = packagePrice * currencyValue;
                await ProcessSuccessfulPayment(customer.Id, packageId, "Flutterwave", finalAmount, transactionId);

                return Json(new { success = true, message = "Payment successful" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing Flutterwave payment");
                return Json(new { success = false, message = "Payment failed" });
            }
        }

        // POST: /Customer/PaymentMollie
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> PaymentMollie()
        {
            try
            {
                var customer = await GetCurrentCustomerAsync();
                if (customer == null)
                {
                    return RedirectToCustomerLogin();
                }

                // Get package information from session
                var packageIdStr = HttpContext.Session.GetString("PackageId");
                var packagePriceStr = HttpContext.Session.GetString("PackagePrice");
                var currencyValueStr = HttpContext.Session.GetString("CurrencyValue");

                if (string.IsNullOrEmpty(packageIdStr) || string.IsNullOrEmpty(packagePriceStr))
                {
                    SetErrorMessage("Session expired. Please try again.");
                    return RedirectToAction("Packages");
                }

                if (!int.TryParse(packageIdStr, out var packageId) ||
                    !decimal.TryParse(packagePriceStr, out var packagePrice) ||
                    !decimal.TryParse(currencyValueStr, out var currencyValue))
                {
                    SetErrorMessage("Invalid package information.");
                    return RedirectToAction("Packages");
                }

                // Process Mollie payment (simplified for demo)
                var finalAmount = packagePrice * currencyValue;
                var transactionId = _paymentService.GenerateTransactionId();
                await ProcessSuccessfulPayment(customer.Id, packageId, "Mollie", finalAmount, transactionId);

                SetSuccessMessage("Mollie payment successful! Your package has been activated.");
                return RedirectToAction("PurchaseHistory");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing Mollie payment");
                SetErrorMessage("Payment failed. Please try again.");
                return RedirectToAction("Packages");
            }
        }

        // Private helper method to process successful payment
        private async Task ProcessSuccessfulPayment(int userId, int packageId, string paymentMethod, decimal amount, string transactionId)
        {
            try
            {
                // Deactivate any existing active packages for the user
                var existingPurchases = await _db.PackagePurchases
                    .Where(pp => pp.UserId == userId && pp.CurrentlyActive == true)
                    .ToListAsync();

                foreach (var existingPurchase in existingPurchases)
                {
                    existingPurchase.CurrentlyActive = false;
                    existingPurchase.UpdatedAt = DateTime.UtcNow;
                }

                // Get package details
                var package = await _db.Packages.FirstOrDefaultAsync(p => p.Id == packageId);
                if (package == null)
                {
                    throw new InvalidOperationException("Package not found");
                }

                // Get currency information from session
                var currencyName = HttpContext.Session.GetString("CurrencyName") ?? "USD";
                var currencySymbol = HttpContext.Session.GetString("CurrencySymbol") ?? "$";

                // Create new package purchase record
                var purchase = new PackagePurchase
                {
                    UserId = userId,
                    PackageId = packageId,
                    PackageStartDate = DateTime.UtcNow,
                    PackageEndDate = DateTime.UtcNow.AddDays(package.ValidDays),
                    PaymentMethod = paymentMethod,
                    PaymentStatus = "Completed",
                    PaidAmount = amount,
                    PaidCurrencyName = currencyName,
                    PaidCurrencySymbol = currencySymbol,
                    TransactionId = transactionId,
                    CurrentlyActive = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _db.PackagePurchases.Add(purchase);
                await _db.SaveChangesAsync();

                // Clear session data
                HttpContext.Session.Remove("PackageId");
                HttpContext.Session.Remove("PackageName");
                HttpContext.Session.Remove("PackagePrice");
                HttpContext.Session.Remove("CurrencyName");
                HttpContext.Session.Remove("CurrencySymbol");
                HttpContext.Session.Remove("CurrencyValue");

                _logger.LogInformation($"Package purchase completed successfully: User {userId}, Package {packageId}, Amount {amount}, Method {paymentMethod}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing successful payment for User {UserId}, Package {PackageId}", userId, packageId);
                throw;
            }
        }

        // POST: /Customer/SubmitReview
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> SubmitReview(int ListingId, int Rating, string? ReviewText)
        {
            try
            {
                var customerId = GetCurrentCustomerId();
                if (customerId == null)
                {
                    return RedirectToAction("Login", "Account", new { Area = "Customer" });
                }

                // Check if user already reviewed this listing
                var existingReview = await _db.Reviews
                    .FirstOrDefaultAsync(r => r.ListingId == ListingId && r.AgentId == customerId.Value && r.AgentType == "User");

                if (existingReview != null)
                {
                    SetErrorMessage("You have already reviewed this listing.");
                    return RedirectToAction("Detail", "Listing", new { id = ListingId });
                }

                var review = new Review
                {
                    ListingId = ListingId,
                    AgentId = customerId.Value,
                    AgentType = "User",
                    Rating = Rating,
                    ReviewText = ReviewText,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _db.Reviews.Add(review);
                await _db.SaveChangesAsync();

                SetSuccessMessage("Your review has been submitted successfully!");
                return RedirectToAction("Detail", "Listing", new { id = ListingId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error submitting review for listing ID: {ListingId}", ListingId);
                SetErrorMessage("An error occurred while submitting your review.");
                return RedirectToAction("Detail", "Listing", new { id = ListingId });
            }
        }

        // GET: /Customer/AddToWishlist/{id}
        public async Task<IActionResult> AddToWishlist(int id)
        {
            try
            {
                var customerId = GetCurrentCustomerId();
                if (customerId == null)
                {
                    return RedirectToAction("Login", "Account", new { Area = "Customer" });
                }

                // Check if already in wishlist
                var existingWishlist = await _db.Wishlists
                    .FirstOrDefaultAsync(w => w.ListingId == id && w.UserId == customerId.Value);

                if (existingWishlist != null)
                {
                    SetErrorMessage("This listing is already in your wishlist.");
                    return RedirectToAction("Detail", "Listing", new { id = id });
                }

                var wishlist = new Wishlist
                {
                    ListingId = id,
                    UserId = customerId.Value,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _db.Wishlists.Add(wishlist);
                await _db.SaveChangesAsync();

                SetSuccessMessage("Listing added to your wishlist successfully!");
                return RedirectToAction("Detail", "Listing", new { id = id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding listing to wishlist. Listing ID: {ListingId}", id);
                SetErrorMessage("An error occurred while adding to wishlist.");
                return RedirectToAction("Detail", "Listing", new { id = id });
            }
        }

        // POST: /Customer/SendMessage
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> SendMessage(int ListingId, string SenderName, string SenderEmail, string? SenderPhone, string MessageText)
        {
            try
            {
                var listing = await _db.Listings
                    .Include(l => l.User)
                    .Include(l => l.Admin)
                    .FirstOrDefaultAsync(l => l.Id == ListingId);

                if (listing == null)
                {
                    SetErrorMessage("Listing not found.");
                    return RedirectToAction("Index", "Home");
                }

                // Get email template for listing inquiry
                var emailTemplate = await _db.EmailTemplates
                    .FirstOrDefaultAsync(et => et.EtName == "Listing Inquiry");

                if (emailTemplate != null)
                {
                    var agentEmail = listing.User?.Email ?? listing.Admin?.Email;
                    var agentName = listing.User?.Name ?? listing.Admin?.Name;

                    if (!string.IsNullOrEmpty(agentEmail))
                    {
                        var subject = emailTemplate.EtSubject;
                        var message = emailTemplate.EtContent;

                        // Replace placeholders
                        message = message.Replace("[[agent_name]]", agentName);
                        message = message.Replace("[[listing_name]]", listing.ListingName);
                        message = message.Replace("[[listing_url]]", Url.Action("Detail", "Listing", new { id = listing.Id }, Request.Scheme));
                        message = message.Replace("[[name]]", SenderName);
                        message = message.Replace("[[email]]", SenderEmail);
                        message = message.Replace("[[phone]]", SenderPhone ?? "");
                        message = message.Replace("[[message]]", MessageText);

                        // Send email (implement email service)
                        // await _emailService.SendEmailAsync(agentEmail, subject, message);
                    }
                }

                SetSuccessMessage("Your message has been sent successfully!");
                return RedirectToAction("Detail", "Listing", new { id = ListingId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending message for listing ID: {ListingId}", ListingId);
                SetErrorMessage("An error occurred while sending your message.");
                return RedirectToAction("Detail", "Listing", new { id = ListingId });
            }
        }

        // POST: /Customer/ReportListing
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ReportListing(int ListingId, string ReporterName, string ReporterEmail, string ReportReason, string ReportDescription)
        {
            try
            {
                // Here you would typically save the report to a database table
                // For now, we'll just log it and send a success message

                _logger.LogInformation("Listing report received - Listing ID: {ListingId}, Reporter: {ReporterName}, Reason: {ReportReason}",
                    ListingId, ReporterName, ReportReason);

                SetSuccessMessage("Your report has been submitted successfully. We will review it shortly.");
                return RedirectToAction("Detail", "Listing", new { id = ListingId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error submitting report for listing ID: {ListingId}", ListingId);
                SetErrorMessage("An error occurred while submitting your report.");
                return RedirectToAction("Detail", "Listing", new { id = ListingId });
            }
        }

        // Helper method for saving listing photos
        private async Task<string?> SaveListingPhoto(IFormFile photo, string subfolder)
        {
            try
            {
                if (photo == null || photo.Length == 0)
                    return null;

                // Validate file type
                var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".webp" };
                var fileExtension = Path.GetExtension(photo.FileName).ToLowerInvariant();

                if (!allowedExtensions.Contains(fileExtension))
                {
                    return null;
                }

                // Validate file size (max 10MB)
                if (photo.Length > 10 * 1024 * 1024)
                {
                    return null;
                }

                // Generate unique filename
                var fileName = $"{Guid.NewGuid()}{fileExtension}";
                var uploadPath = Path.Combine("wwwroot", "uploads", subfolder);

                if (!Directory.Exists(uploadPath))
                {
                    Directory.CreateDirectory(uploadPath);
                }

                var filePath = Path.Combine(uploadPath, fileName);
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await photo.CopyToAsync(stream);
                }

                return fileName;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving listing photo");
                return null;
            }
        }

        // Helper method for saving listing videos
        private async Task<string?> SaveListingVideo(IFormFile video)
        {
            try
            {
                if (video == null || video.Length == 0)
                    return null;

                // Validate file type
                var allowedExtensions = new[] { ".mp4", ".avi", ".mov", ".wmv", ".flv", ".webm" };
                var fileExtension = Path.GetExtension(video.FileName).ToLowerInvariant();

                if (!allowedExtensions.Contains(fileExtension))
                {
                    return null;
                }

                // Validate file size (max 100MB)
                if (video.Length > 100 * 1024 * 1024)
                {
                    return null;
                }

                // Generate unique filename
                var fileName = $"{Guid.NewGuid()}{fileExtension}";
                var uploadPath = Path.Combine("wwwroot", "uploads", "listing_videos");

                if (!Directory.Exists(uploadPath))
                {
                    Directory.CreateDirectory(uploadPath);
                }

                var filePath = Path.Combine(uploadPath, fileName);
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await video.CopyToAsync(stream);
                }

                return fileName;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving listing video");
                return null;
            }
        }

        // Helper method for deleting listing photos
        private void DeleteListingPhoto(string fileName, string subfolder)
        {
            try
            {
                if (string.IsNullOrEmpty(fileName))
                    return;

                var filePath = Path.Combine("wwwroot", "uploads", subfolder, fileName);
                if (System.IO.File.Exists(filePath))
                {
                    System.IO.File.Delete(filePath);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting listing photo: {FileName}", fileName);
            }
        }

        // Helper method for deleting listing videos
        private void DeleteListingVideo(string fileName)
        {
            try
            {
                if (string.IsNullOrEmpty(fileName))
                    return;

                var filePath = Path.Combine("wwwroot", "uploads", "listing_videos", fileName);
                if (System.IO.File.Exists(filePath))
                {
                    System.IO.File.Delete(filePath);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting listing video: {FileName}", fileName);
            }
        }

        private int? GetCurrentCustomerId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId))
            {
                return userId;
            }
            return null;
        }

        // Helper method removed - now using BaseController methods
    }
}
