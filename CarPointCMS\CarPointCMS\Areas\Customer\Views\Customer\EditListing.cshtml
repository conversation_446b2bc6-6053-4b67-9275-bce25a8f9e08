@using CarPointCMS.Models.Entities
@using CarPointCMS.Models.ViewModels
@model CustomerEditListingViewModel
@{
    ViewData["Title"] = "Edit Listing";
}

<div class="page-banner" style="background-image: url('~/uploads/page_banners/@Model.PageOther?.CustomerPanelPageBanner')">
    <div class="page-banner-bg"></div>
    <h1>Edit Listing</h1>
    <nav>
        <ol class="breadcrumb justify-content-center">
            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Home</a></li>
            <li class="breadcrumb-item active">Edit Listing</li>
        </ol>
    </nav>
</div>

<div class="page-content">
    <div class="container">
        <div class="row">
            <div class="col-md-3">
                <div class="user-sidebar">
                    @await Component.InvokeAsync("CustomerSidebar")
                </div>
            </div>
            <div class="col-md-9">
                @if(!string.IsNullOrEmpty(Model.ListingErrorMessage))
                {
                    <div class="alert alert-danger">@Model.ListingErrorMessage</div>
                }

                @if (!ViewData.ModelState.IsValid)
                {
                    <div asp-validation-summary="All" class="alert alert-danger"></div>
                }

                <form asp-action="EditListing" asp-controller="Customer" asp-route-id="@Model.Id" method="post" enctype="multipart/form-data">
                    @Html.AntiForgeryToken()

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label asp-for="ListingName">Listing Name *</label>
                                <input asp-for="ListingName" class="form-control" />
                                <span asp-validation-for="ListingName" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label asp-for="ListingSlug">Listing Slug *</label>
                                <input asp-for="ListingSlug" class="form-control" />
                                <span asp-validation-for="ListingSlug" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label asp-for="ListingDescription">Listing Description *</label>
                                <textarea asp-for="ListingDescription" class="form-control editor" cols="30" rows="10"></textarea>
                                <span asp-validation-for="ListingDescription" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="ListingBrandId">Brand *</label>
                                <select asp-for="ListingBrandId" asp-items="Model.Brands" class="form-control select2">
                                    <option value="">Select Brand</option>
                                </select>
                                <span asp-validation-for="ListingBrandId" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="ListingLocationId">Location *</label>
                                <select asp-for="ListingLocationId" asp-items="Model.Locations" class="form-control select2">
                                    <option value="">Select Location</option>
                                </select>
                                <span asp-validation-for="ListingLocationId" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="ListingAddress">Address</label>
                                <textarea asp-for="ListingAddress" class="form-control h-70" cols="30" rows="3"></textarea>
                                <span asp-validation-for="ListingAddress" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="ListingPhone">Phone Number</label>
                                <input asp-for="ListingPhone" type="text" class="form-control" />
                                <span asp-validation-for="ListingPhone" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="ListingEmail">Email Address</label>
                                <input asp-for="ListingEmail" type="email" class="form-control" />
                                <span asp-validation-for="ListingEmail" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="ListingMap">Map iFrame Code</label>
                                <textarea asp-for="ListingMap" class="form-control h-70" cols="30" rows="3"></textarea>
                                <span asp-validation-for="ListingMap" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label asp-for="ListingWebsite">Website</label>
                                <input asp-for="ListingWebsite" type="url" class="form-control" />
                                <span asp-validation-for="ListingWebsite" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label>Current Featured Photo</label>
                                <div>
                                    @if(!string.IsNullOrEmpty(Model.CurrentFeaturedPhoto))
                                    {
                                        <img src="~/uploads/listing_featured_photos/@Model.CurrentFeaturedPhoto" class="w-200" alt="Current Featured Photo">
                                    }
                                    else
                                    {
                                        <span class="text-muted">No featured photo</span>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label asp-for="ListingFeaturedPhoto">Change Featured Photo</label>
                                <input asp-for="ListingFeaturedPhoto" type="file" class="form-control" accept="image/*" />
                                <span asp-validation-for="ListingFeaturedPhoto" class="text-danger"></span>
                                <small class="form-text text-muted">Leave empty to keep current photo</small>
                            </div>
                        </div>
                    </div>

                    <h4 class="mt_30">Features</h4>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="ListingPrice">Price *</label>
                                <input asp-for="ListingPrice" type="number" class="form-control" step="0.01" />
                                <span asp-validation-for="ListingPrice" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="ListingModel">Model</label>
                                <input asp-for="ListingModel" type="text" class="form-control" />
                                <span asp-validation-for="ListingModel" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <h4 class="mt_30">Car Details</h4>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="ListingExteriorColor">Exterior Color</label>
                                <input asp-for="ListingExteriorColor" type="text" class="form-control" />
                                <span asp-validation-for="ListingExteriorColor" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="ListingInteriorColor">Interior Color</label>
                                <input asp-for="ListingInteriorColor" type="text" class="form-control" />
                                <span asp-validation-for="ListingInteriorColor" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="ListingCylinder">Cylinder</label>
                                <input asp-for="ListingCylinder" type="text" class="form-control" />
                                <span asp-validation-for="ListingCylinder" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="ListingFuelType">Fuel Type</label>
                                <select asp-for="ListingFuelType" class="form-control">
                                    <option value="">Select Fuel Type</option>
                                    <option value="Petrol">Petrol</option>
                                    <option value="Diesel">Diesel</option>
                                    <option value="Electric">Electric</option>
                                    <option value="Hybrid">Hybrid</option>
                                    <option value="CNG">CNG</option>
                                    <option value="LPG">LPG</option>
                                </select>
                                <span asp-validation-for="ListingFuelType" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="ListingTransmission">Transmission</label>
                                <select asp-for="ListingTransmission" class="form-control">
                                    <option value="">Select Transmission</option>
                                    <option value="Manual">Manual</option>
                                    <option value="Automatic">Automatic</option>
                                    <option value="CVT">CVT</option>
                                    <option value="Semi-Automatic">Semi-Automatic</option>
                                </select>
                                <span asp-validation-for="ListingTransmission" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="ListingEngineSize">Engine Size</label>
                                <input asp-for="ListingEngineSize" type="text" class="form-control" placeholder="e.g., 1.6L" />
                                <span asp-validation-for="ListingEngineSize" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="ListingVin">VIN Number</label>
                                <input asp-for="ListingVin" type="text" class="form-control" />
                                <span asp-validation-for="ListingVin" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="ListingCondition">Condition</label>
                                <select asp-for="ListingCondition" class="form-control">
                                    <option value="">Select Condition</option>
                                    <option value="New">New</option>
                                    <option value="Used">Used</option>
                                    <option value="Certified Pre-Owned">Certified Pre-Owned</option>
                                </select>
                                <span asp-validation-for="ListingCondition" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="ListingSeat">Seats</label>
                                <select asp-for="ListingSeat" class="form-control">
                                    <option value="">Select Seats</option>
                                    <option value="2">2 Seats</option>
                                    <option value="4">4 Seats</option>
                                    <option value="5">5 Seats</option>
                                    <option value="7">7 Seats</option>
                                    <option value="8">8 Seats</option>
                                    <option value="9">9+ Seats</option>
                                </select>
                                <span asp-validation-for="ListingSeat" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="ListingDriveType">Drive Type</label>
                                <select asp-for="ListingDriveType" class="form-control">
                                    <option value="">Select Drive Type</option>
                                    <option value="FWD">Front Wheel Drive (FWD)</option>
                                    <option value="RWD">Rear Wheel Drive (RWD)</option>
                                    <option value="AWD">All Wheel Drive (AWD)</option>
                                    <option value="4WD">Four Wheel Drive (4WD)</option>
                                </select>
                                <span asp-validation-for="ListingDriveType" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="ListingDoor">Doors</label>
                                <select asp-for="ListingDoor" class="form-control">
                                    <option value="">Select Doors</option>
                                    <option value="2">2 Doors</option>
                                    <option value="3">3 Doors</option>
                                    <option value="4">4 Doors</option>
                                    <option value="5">5 Doors</option>
                                </select>
                                <span asp-validation-for="ListingDoor" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="ListingMileage">Mileage</label>
                                <input asp-for="ListingMileage" type="number" class="form-control" placeholder="Miles/Kilometers" />
                                <span asp-validation-for="ListingMileage" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="ListingModelYear">Model Year</label>
                                <select asp-for="ListingModelYear" class="form-control">
                                    <option value="">Select Year</option>
                                    @for(int year = DateTime.Now.Year + 1; year >= 1990; year--)
                                    {
                                        <option value="@year">@year</option>
                                    }
                                </select>
                                <span asp-validation-for="ListingModelYear" class="text-danger"></span>
                            </div>
                        </div>
                    </div>



                    <h4 class="mt_30">Existing Social Media</h4>
                    <div class="row">
                        @if(!Model.ExistingSocialItems.Any())
                        {
                            <div class="col-md-12">
                                <span class="text-muted">No social media items found</span>
                            </div>
                        }
                        else
                        {
                            <div class="col-md-12">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Platform</th>
                                                <th>URL</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach(var item in Model.ExistingSocialItems)
                                            {
                                                <tr>
                                                    <td>
                                                        @{
                                                            var iconCode = item.ListingSocialName switch
                                                            {
                                                                "Facebook" => "fab fa-facebook-f",
                                                                "Twitter" => "fab fa-twitter",
                                                                "LinkedIn" => "fab fa-linkedin-in",
                                                                "YouTube" => "fab fa-youtube",
                                                                "Pinterest" => "fab fa-pinterest-p",
                                                                "GooglePlus" => "fab fa-google-plus-g",
                                                                "Instagram" => "fab fa-instagram",
                                                                _ => "fas fa-link"
                                                            };
                                                        }
                                                        <i class="@iconCode"></i> @item.ListingSocialName
                                                    </td>
                                                    <td>@item.ListingSocialUrl</td>
                                                    <td>
                                                        <button type="button" class="btn btn-danger btn-sm delete-social-item" data-id="@item.Id">
                                                            <i class="fas fa-trash"></i> Delete
                                                        </button>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        }
                    </div>

                    <h4 class="mt_30">Add Social Media</h4>
                    <div class="row">
                        <div class="col-md-12">
                            <div id="social-media-container">
                                <div class="social-media-item row mb-3">
                                    <div class="col-md-4">
                                        <select name="SocialIcons[0]" class="form-control">
                                            <option value="">Select Social Media</option>
                                            <option value="Facebook">Facebook</option>
                                            <option value="Twitter">Twitter</option>
                                            <option value="LinkedIn">LinkedIn</option>
                                            <option value="YouTube">YouTube</option>
                                            <option value="Pinterest">Pinterest</option>
                                            <option value="GooglePlus">Google Plus</option>
                                            <option value="Instagram">Instagram</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <input type="url" name="SocialUrls[0]" class="form-control" placeholder="Social Media URL">
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-success btn-sm add-social-media">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <h4 class="mt_30">Amenities</h4>
                    <div class="row">
                        @{
                            var i = 0;
                        }
                        @foreach(var amenity in Model.Amenities)
                        {
                            i++;
                            <div class="col-md-4">
                                <div class="form-check mb_10">
                                    <input class="form-check-input amenity_check" name="SelectedAmenityIds" type="checkbox" value="@amenity.Value" id="amenities@i" @(Model.ExistingAmenityIds.Contains(int.Parse(amenity.Value)) ? "checked" : "")>
                                    <label class="form-check-label" for="amenities@i">
                                        @amenity.Text
                                    </label>
                                </div>
                            </div>
                        }
                    </div>

                    <h4 class="mt_30">Existing Photos</h4>
                    <div class="row">
                        @if(!Model.ExistingPhotos.Any())
                        {
                            <div class="col-md-12">
                                <span class="text-muted">No photos found</span>
                            </div>
                        }
                        else
                        {
                            @foreach(var photo in Model.ExistingPhotos)
                            {
                                <div class="col-md-3 mb-3">
                                    <div class="card">
                                        <img src="~/uploads/listing_photos/@photo.ListingPhotoName" class="card-img-top" alt="Listing Photo" style="height: 150px; object-fit: cover;">
                                        <div class="card-body p-2">
                                            <button type="button" class="btn btn-danger btn-sm btn-block delete-photo" data-id="@photo.Id">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            }
                        }
                    </div>

                    <h4 class="mt_30">Add Photos</h4>
                    <div class="row">
                        <div class="col-md-12">
                            <div id="photo-container">
                                <div class="photo-item row mb-3">
                                    <div class="col-md-10">
                                        <input type="file" name="ListingPhotos" class="form-control" accept="image/*" multiple>
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-success btn-sm add-photo">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <h4 class="mt_30">Existing Videos</h4>
                    <div class="row">
                        @if(!Model.ExistingVideos.Any())
                        {
                            <div class="col-md-12">
                                <span class="text-muted">No videos found</span>
                            </div>
                        }
                        else
                        {
                            <div class="col-md-12">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>YouTube Video ID</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach(var video in Model.ExistingVideos)
                                            {
                                                <tr>
                                                    <td>@video.VideoId</td>
                                                    <td>
                                                        <button type="button" class="btn btn-danger btn-sm delete-video" data-id="@video.Id">
                                                            <i class="fas fa-trash"></i> Delete
                                                        </button>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        }
                    </div>

                    <h4 class="mt_30">Add Videos</h4>
                    <div class="row">
                        <div class="col-md-12">
                            <div id="video-container">
                                <div class="video-item row mb-3">
                                    <div class="col-md-10">
                                        <input type="text" name="YoutubeVideoIds[0]" class="form-control" placeholder="YouTube Video ID (e.g., dQw4w9WgXcQ)">
                                        <small class="form-text text-muted">Enter only the video ID from YouTube URL</small>
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-success btn-sm add-video">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <h4 class="mt_30">Existing Additional Features</h4>
                    <div class="row">
                        @if(!Model.ExistingAdditionalFeatures.Any())
                        {
                            <div class="col-md-12">
                                <span class="text-muted">No additional features found</span>
                            </div>
                        }
                        else
                        {
                            <div class="col-md-12">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Feature</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach(var feature in Model.ExistingAdditionalFeatures)
                                            {
                                                <tr>
                                                    <td>@feature.ListingAdditionalFeatureName</td>
                                                    <td>
                                                        <button type="button" class="btn btn-danger btn-sm delete-feature" data-id="@feature.Id">
                                                            <i class="fas fa-trash"></i> Delete
                                                        </button>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        }
                    </div>

                    <h4 class="mt_30">Add Additional Features</h4>
                    <div class="row">
                        <div class="col-md-12">
                            <div id="additional-features-container">
                                <div class="additional-feature-item row mb-3">
                                    <div class="col-md-10">
                                        <input type="text" name="AdditionalFeatures[0]" class="form-control" placeholder="Additional Feature">
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-success btn-sm add-additional-feature">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Hidden fields for deletions -->
                    <input type="hidden" asp-for="PhotosToDelete" id="photos-to-delete" />
                    <input type="hidden" asp-for="VideosToDelete" id="videos-to-delete" />

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-primary">Update Listing</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
<script>
$(document).ready(function() {
    var socialMediaIndex = 1;
    var additionalFeatureIndex = 1;
    var photosToDelete = [];
    var videosToDelete = [];

    // Social Media Add/Remove Functionality
    $(document).on('click', '.add-social-media', function() {
        var socialMediaHtml = `
            <div class="social-media-item row mb-3">
                <div class="col-md-4">
                    <select name="SocialIcons[${socialMediaIndex}]" class="form-control">
                        <option value="">Select Social Media</option>
                        <option value="Facebook">Facebook</option>
                        <option value="Twitter">Twitter</option>
                        <option value="LinkedIn">LinkedIn</option>
                        <option value="YouTube">YouTube</option>
                        <option value="Pinterest">Pinterest</option>
                        <option value="GooglePlus">Google Plus</option>
                        <option value="Instagram">Instagram</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <input type="url" name="SocialUrls[${socialMediaIndex}]" class="form-control" placeholder="Social Media URL">
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-danger btn-sm remove-social-media">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
        `;
        $('#social-media-container').append(socialMediaHtml);
        socialMediaIndex++;
    });

    $(document).on('click', '.remove-social-media', function() {
        $(this).closest('.social-media-item').remove();
    });

    // Photo Add/Remove Functionality
    $(document).on('click', '.add-photo', function() {
        var photoHtml = `
            <div class="photo-item row mb-3">
                <div class="col-md-10">
                    <input type="file" name="ListingPhotos" class="form-control" accept="image/*" multiple>
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-danger btn-sm remove-photo">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
        `;
        $('#photo-container').append(photoHtml);
    });

    $(document).on('click', '.remove-photo', function() {
        $(this).closest('.photo-item').remove();
    });

    // Delete existing photo functionality
    $(document).on('click', '.delete-photo', function() {
        if (confirm('Are you sure you want to delete this photo?')) {
            var photoId = $(this).data('id');
            photosToDelete.push(photoId);
            $('#photos-to-delete').val(photosToDelete.join(','));
            $(this).closest('.col-md-3').remove();
        }
    });

    // Video Add/Remove Functionality
    var videoIndex = 1;
    $(document).on('click', '.add-video', function() {
        var videoHtml = `
            <div class="video-item row mb-3">
                <div class="col-md-10">
                    <input type="text" name="YoutubeVideoIds[${videoIndex}]" class="form-control" placeholder="YouTube Video ID (e.g., dQw4w9WgXcQ)">
                    <small class="form-text text-muted">Enter only the video ID from YouTube URL</small>
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-danger btn-sm remove-video">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
        `;
        $('#video-container').append(videoHtml);
        videoIndex++;
    });

    $(document).on('click', '.remove-video', function() {
        $(this).closest('.video-item').remove();
    });

    // Delete existing video functionality
    $(document).on('click', '.delete-video', function() {
        if (confirm('Are you sure you want to delete this video?')) {
            var videoId = $(this).data('id');
            videosToDelete.push(videoId);
            $('#videos-to-delete').val(videosToDelete.join(','));
            $(this).closest('tr').remove();
        }
    });

    // Additional Features Add/Remove Functionality
    $(document).on('click', '.add-additional-feature', function() {
        var featureHtml = `
            <div class="additional-feature-item row mb-3">
                <div class="col-md-10">
                    <input type="text" name="AdditionalFeatures[${additionalFeatureIndex}]" class="form-control" placeholder="Additional Feature">
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-danger btn-sm remove-additional-feature">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
        `;
        $('#additional-features-container').append(featureHtml);
        additionalFeatureIndex++;
    });

    $(document).on('click', '.remove-additional-feature', function() {
        $(this).closest('.additional-feature-item').remove();
    });

    // Delete existing additional feature functionality
    $(document).on('click', '.delete-feature', function() {
        if (confirm('Are you sure you want to delete this feature?')) {
            $(this).closest('tr').remove();
        }
    });

    // Delete existing social media item functionality
    $(document).on('click', '.delete-social-item', function() {
        if (confirm('Are you sure you want to delete this social media item?')) {
            $(this).closest('tr').remove();
        }
    });

    // Initialize Select2 for better dropdowns
    if (typeof $.fn.select2 !== 'undefined') {
        $('.select2').select2({
            placeholder: 'Select an option',
            allowClear: true
        });
    }

    // Auto-generate slug from listing name
    $('input[name="ListingName"]').on('keyup', function() {
        var name = $(this).val();
        var slug = name.toLowerCase()
            .replace(/[^a-z0-9 -]/g, '') // Remove invalid chars
            .replace(/\s+/g, '-') // Replace spaces with -
            .replace(/-+/g, '-') // Replace multiple - with single -
            .trim('-'); // Trim - from start and end

        if ($('input[name="ListingSlug"]').length) {
            $('input[name="ListingSlug"]').val(slug);
        }
    });

    // Price formatting
    $('input[name="ListingPrice"]').on('keyup', function() {
        var value = $(this).val().replace(/[^0-9.]/g, '');
        $(this).val(value);
    });

    // Character counter for description
    $('textarea[name="ListingDescription"]').on('keyup', function() {
        var maxLength = 1000;
        var currentLength = $(this).val().length;
        var remaining = maxLength - currentLength;

        if (!$(this).next('.char-counter').length) {
            $(this).after('<small class="char-counter text-muted"></small>');
        }

        $(this).next('.char-counter').text(remaining + ' characters remaining');

        if (remaining < 0) {
            $(this).next('.char-counter').removeClass('text-muted').addClass('text-danger');
        } else {
            $(this).next('.char-counter').removeClass('text-danger').addClass('text-muted');
        }
    });

    // YouTube Video ID validation
    $(document).on('blur', 'input[name^="YoutubeVideoIds"]', function() {
        var videoId = $(this).val().trim();
        if (videoId && !/^[a-zA-Z0-9_-]{11}$/.test(videoId)) {
            $(this).addClass('is-invalid');
            if (!$(this).next('.invalid-feedback').length) {
                $(this).after('<div class="invalid-feedback">Please enter a valid YouTube video ID (11 characters)</div>');
            }
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
        }
    });
});
</script>
}
