using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc.Rendering;
using CarPointCMS.Models.Entities;

namespace CarPointCMS.Models.ViewModels
{
    public class CustomerEditListingViewModel : CustomerListingCreateViewModel
    {
        public int Id { get; set; }

        public string? CurrentFeaturedPhoto { get; set; }

        // Existing related data
        public List<ListingPhoto> ExistingPhotos { get; set; } = new List<ListingPhoto>();
        public List<ListingVideo> ExistingVideos { get; set; } = new List<ListingVideo>();
        public List<ListingSocialItem> ExistingSocialItems { get; set; } = new List<ListingSocialItem>();
        public List<ListingAdditionalFeature> ExistingAdditionalFeatures { get; set; } = new List<ListingAdditionalFeature>();
        public List<int> ExistingAmenityIds { get; set; } = new List<int>();

        // For handling deletions
        public List<int> PhotosToDelete { get; set; } = new List<int>();
        public List<int> VideosToDelete { get; set; } = new List<int>();

        // Override the inherited YoutubeVideoIds to add new videos
        public new List<string> YoutubeVideoIds { get; set; } = new List<string>();

        // Override required validation for featured photo since it already exists
        [Required(ErrorMessage = "Featured photo is required")]
        public new IFormFile? ListingFeaturedPhoto { get; set; }

        // Page data for the view
        public PageOtherItem? PageOtherItem { get; set; }
        public PageOtherItem? PageOther { get; set; }
        public string? ListingErrorMessage { get; set; }
    }
}
