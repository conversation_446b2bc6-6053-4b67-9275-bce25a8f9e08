using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc.Rendering;
using CarPointCMS.Models.Entities;

namespace CarPointCMS.Models.ViewModels
{
    public class CustomerListingCreateViewModel
    {
        [Required(ErrorMessage = "Listing name is required")]
        [StringLength(255, ErrorMessage = "Listing name cannot exceed 255 characters")]
        public string ListingName { get; set; } = "";

        [Required(ErrorMessage = "Listing slug is required")]
        [StringLength(255, ErrorMessage = "Listing slug cannot exceed 255 characters")]
        public string ListingSlug { get; set; } = "";

        [Required(ErrorMessage = "Description is required")]
        public string ListingDescription { get; set; } = "";

        [StringLength(500, ErrorMessage = "Address cannot exceed 500 characters")]
        public string? ListingAddress { get; set; }

        [Phone(ErrorMessage = "Invalid phone number format")]
        [StringLength(20, ErrorMessage = "Phone number cannot exceed 20 characters")]
        public string? ListingPhone { get; set; }

        [EmailAddress(ErrorMessage = "Invalid email format")]
        [StringLength(255, ErrorMessage = "Email cannot exceed 255 characters")]
        public string? ListingEmail { get; set; }

        [Url(ErrorMessage = "Invalid website URL")]
        [StringLength(255, ErrorMessage = "Website URL cannot exceed 255 characters")]
        public string? ListingWebsite { get; set; }

        public string? ListingMap { get; set; }

        [Required(ErrorMessage = "Price is required")]
        [Range(0, double.MaxValue, ErrorMessage = "Price must be a positive number")]
        public decimal ListingPrice { get; set; }

        [StringLength(50, ErrorMessage = "Exterior color cannot exceed 50 characters")]
        public string? ListingExteriorColor { get; set; }

        [StringLength(50, ErrorMessage = "Interior color cannot exceed 50 characters")]
        public string? ListingInteriorColor { get; set; }

        [StringLength(50, ErrorMessage = "Model cannot exceed 50 characters")]
        public string? ListingModel { get; set; }

        [Range(1900, 2030, ErrorMessage = "Model year must be between 1900 and 2030")]
        public int? ListingModelYear { get; set; }

        [Range(0, int.MaxValue, ErrorMessage = "Mileage must be a positive number")]
        public int? ListingMileage { get; set; }

        [StringLength(50, ErrorMessage = "Fuel type cannot exceed 50 characters")]
        public string? ListingFuelType { get; set; }

        [StringLength(50, ErrorMessage = "Transmission cannot exceed 50 characters")]
        public string? ListingTransmission { get; set; }

        [StringLength(50, ErrorMessage = "Condition cannot exceed 50 characters")]
        public string? ListingCondition { get; set; }

        [StringLength(50, ErrorMessage = "Drive type cannot exceed 50 characters")]
        public string? ListingDriveType { get; set; }

        [StringLength(50, ErrorMessage = "Engine size cannot exceed 50 characters")]
        public string? ListingEngineSize { get; set; }

        [StringLength(50, ErrorMessage = "Cylinder cannot exceed 50 characters")]
        public string? ListingCylinder { get; set; }

        [Range(1, 10, ErrorMessage = "Number of doors must be between 1 and 10")]
        public int? ListingDoor { get; set; }

        [Range(1, 50, ErrorMessage = "Number of seats must be between 1 and 50")]
        public int? ListingSeat { get; set; }

        [StringLength(100, ErrorMessage = "VIN cannot exceed 100 characters")]
        public string? ListingVin { get; set; }

        // Foreign Keys
        [Required(ErrorMessage = "Brand is required")]
        public int ListingBrandId { get; set; }

        [Required(ErrorMessage = "Location is required")]
        public int ListingLocationId { get; set; }

        // File Uploads
        [Required(ErrorMessage = "Featured photo is required")]
        public IFormFile? ListingFeaturedPhoto { get; set; }

        public List<IFormFile>? ListingPhotos { get; set; }

        // YouTube Video IDs (not file uploads)
        public List<string> YoutubeVideoIds { get; set; } = new List<string>();

        // Amenities and Features
        public List<int> SelectedAmenityIds { get; set; } = new List<int>();
        public List<string> AdditionalFeatures { get; set; } = new List<string>();
        public List<string> SocialIcons { get; set; } = new List<string>();
        public List<string> SocialUrls { get; set; } = new List<string>();

        // Dropdown Lists (populated by controller)
        public List<SelectListItem> Brands { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> Locations { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> Amenities { get; set; } = new List<SelectListItem>();
    }

    public class CustomerAddListingViewModel : CustomerListingCreateViewModel
    {
        public PageOtherItem? PageOtherItem { get; set; }
        public PageOtherItem? PageOther { get; set; }
        public PackagePurchase? CurrentPackage { get; set; }
        public string? ListingErrorMessage { get; set; }
    }
}
